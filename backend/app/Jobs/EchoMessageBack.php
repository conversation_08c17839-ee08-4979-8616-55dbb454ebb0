<?php

namespace App\Jobs;

use App\Events\MessageSent;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class EchoMessageBack implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $message;

    /**
     * Create a new job instance.
     */
    public function __construct(string $message)
    {
        $this->message = $message;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // Log before broadcasting
            Log::info('Broadcasting message: ' . $this->message);

            // Broadcast the message
            broadcast(new MessageSent($this->message));

            // Log after broadcasting
            Log::info('Message broadcast complete');
        } catch (\Exception $e) {
            Log::error('Error broadcasting message: ' . $e->getMessage());
        }
    }
}
