<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import echo from '@/services/echo'
import { useAuthStore } from '@/stores/auth'

const messages = ref<{text: string, isSelf: boolean}[]>([])
const newMessage = ref('')
const authStore = useAuthStore()

const sendMessage = async () => {
  if (!newMessage.value.trim()) return

  try {
    // Add message to local list immediately (as self)
    messages.value.push({
      text: newMessage.value,
      isSelf: true
    })

    // Send to server
    await fetch(`${import.meta.env.VITE_API_URL}/api/messages`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authStore.token}`
      },
      body: JSON.stringify({ message: newMessage.value })
    })

    // Clear input after sending
    newMessage.value = ''
  } catch (error) {
    console.error('Failed to send message:', error)
  }
}

onMounted(() => {
  console.log('ChatBox: Setting up Echo listener on chat channel');

  const channel = echo.channel('chat');

  // Listen for all events on this channel for debugging
  channel.listen('*', (eventName: string, data: any) => {
    console.log('ChatBox: Received any event:', eventName, data);
  });

  channel.listen('.MessageSent', (e: { message: string }) => {
    console.log('ChatBox: Received .MessageSent event:', e);
    messages.value.push({
      text: e.message,
      isSelf: false
    });
  });

  console.log('ChatBox: Echo listener setup complete');
})

onUnmounted(() => {
  console.log('ChatBox: Cleaning up Echo listener');
  // Clean up listeners
  echo.leave('chat');
  console.log('ChatBox: Echo listener cleanup complete');
})
</script>

<template>
  <div class="chat-box">
    <h2>Real-time Chat</h2>

    <div class="messages-container">
      <div v-if="messages.length === 0" class="no-messages">
        No messages yet. Start the conversation!
      </div>
      <div v-else class="messages">
        <div
          v-for="(msg, index) in messages"
          :key="index"
          class="message"
          :class="{ 'self': msg.isSelf }"
        >
          {{ msg.text }}
        </div>
      </div>
    </div>

    <div class="message-input">
      <input
        v-model="newMessage"
        @keyup.enter="sendMessage"
        placeholder="Type a message..."
        type="text"
      />
      <button @click="sendMessage">Send</button>
    </div>
  </div>
</template>

<style scoped>
.chat-box {
  width: 100%;
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

h2 {
  padding: 1rem;
  margin: 0;
  background-color: #f5f5f5;
  border-bottom: 1px solid #eee;
}

.messages-container {
  height: 300px;
  overflow-y: auto;
  padding: 1rem;
}

.no-messages {
  color: #999;
  text-align: center;
  padding: 2rem;
}

.messages {
  display: flex;
  flex-direction: column;
}

.message {
  max-width: 80%;
  padding: 0.75rem 1rem;
  margin-bottom: 0.75rem;
  border-radius: 18px;
  background-color: #f1f1f1;
  align-self: flex-start;
}

.message.self {
  background-color: #42b883;
  color: white;
  align-self: flex-end;
}

.message-input {
  display: flex;
  padding: 1rem;
  border-top: 1px solid #eee;
}

input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-right: 0.5rem;
}

button {
  padding: 0.75rem 1.5rem;
  background-color: #42b883;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background-color: #3aa876;
}
</style>
